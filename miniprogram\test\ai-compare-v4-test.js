/**
 * AI产品对比V4版本测试脚本
 * 对比产品：华为 Mate 70 Pro 和 苹果iPhone 16 Pro
 * 对应后端路由：POST /api/v1/products/compare-v4
 * 使用方法：node ai-compare-v4-test.js
 */

const fs = require('fs');
const path = require('path');

// 配置后端服务器地址
const BASE_URL = 'http://localhost:5000/api/v1';

/**
 * 保存数据到JSON文件
 */
function saveToJsonFile(data, filename = 'ai-compare-v4-result.json') {
  const filePath = path.join(__dirname, filename);
  const jsonData = JSON.stringify(data, null, 2);

  try {
    fs.writeFileSync(filePath, jsonData, 'utf8');
    console.log(`✅ 数据已保存到: ${filePath}`);
  } catch (error) {
    console.error('❌ 保存文件失败:', error);
  }
}

/**
 * 发送HTTP请求 (Node.js环境)
 */
function sendRequest(url, data, method = 'POST', headers = {}) {
  return new Promise((resolve, reject) => {
    const https = require('https');
    const http = require('http');
    const urlObj = new URL(url);

    const postData = data ? JSON.stringify(data) : '';
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port,
      path: urlObj.pathname,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData),
        ...headers
      }
    };

    const protocol = urlObj.protocol === 'https:' ? https : http;

    const req = protocol.request(options, (res) => {
      let responseData = '';

      res.on('data', (chunk) => {
        responseData += chunk;
      });

      res.on('end', () => {
        try {
          const jsonData = JSON.parse(responseData);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: jsonData
          });
        } catch (error) {
          reject(new Error('响应数据解析失败: ' + error.message));
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (postData) {
      req.write(postData);
    }
    req.end();
  });
}

/**
 * 执行AI产品对比V4测试
 */
async function testAICompareV4() {
  console.log('🧪 开始测试AI产品对比V4接口...');
  console.log('📱 测试产品: 华为 Mate 70 Pro vs 苹果iPhone 16 Pro');

  const requestData = {
    productNames: ['华为 Mate 70 Pro', '苹果iPhone 16 Pro']
  };
  
  const apiUrl = `${BASE_URL}/products/compare-v4`;

  try {
    console.log('📡 正在发送请求...');
    console.log('请求数据:', requestData);
    
    const response = await sendRequest(apiUrl, requestData, 'POST');
    
    console.log('✅ 请求完成!');
    console.log('📊 HTTP状态码:', response.statusCode);
    console.log('📊 响应数据概览:', {
      success: response.data.success,
      message: response.data.message,
      dataKeys: response.data.data ? Object.keys(response.data.data) : []
    });
    
    // 保存完整响应数据（不做任何处理）
    const fullResponse = {
      request: {
        url: apiUrl,
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: requestData
      },
      response: {
        statusCode: response.statusCode,
        headers: response.headers,
        data: response.data  // 完整保存原始数据结构
      },
      timestamp: new Date().toISOString()
    };
    
    saveToJsonFile(fullResponse);
    
    if (response.statusCode === 200 && response.data.success) {
      console.log('🎉 AI产品对比V4测试成功!');
      
      // 显示一些关键信息（但不修改保存的数据）
      if (response.data.data) {
        const data = response.data.data;
        console.log('📊 对比结果概览:');
        console.log(`   - 产品数量: ${data.products ? data.products.length : 0}`);
        console.log(`   - AI分析: ${data.aiAnalysis ? '已生成' : '未生成'}`);
        console.log(`   - 版本: ${data.meta ? data.meta.version : '未知'}`);
      }
    } else {
      console.log('⚠️  AI产品对比V4请求返回非成功状态');
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    
    // 保存错误信息
    const errorResponse = {
      request: {
        url: apiUrl,
        method: 'POST',
        body: requestData
      },
      error: {
        message: error.message,
        stack: error.stack
      },
      timestamp: new Date().toISOString()
    };
    
    saveToJsonFile(errorResponse, 'ai-compare-v4-error.json');
  }
}

/**
 * 测试多产品对比（3个产品）
 */
async function testMultiProductCompare() {
  console.log('🧪 开始测试多产品AI对比V4接口...');
  console.log('📱 测试产品: 华为 Mate 70 Pro vs 苹果iPhone 16 Pro vs 小米15 Pro');

  const requestData = {
    productNames: ['华为 Mate 70 Pro', '苹果iPhone 16 Pro', '小米15 Pro']
  };
  
  const apiUrl = `${BASE_URL}/products/compare-v4`;

  try {
    console.log('📡 正在发送多产品对比请求...');
    console.log('请求数据:', requestData);
    
    const response = await sendRequest(apiUrl, requestData, 'POST');
    
    console.log('✅ 多产品对比请求完成!');
    console.log('📊 HTTP状态码:', response.statusCode);
    
    // 保存完整响应数据
    const fullResponse = {
      request: {
        url: apiUrl,
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: requestData
      },
      response: {
        statusCode: response.statusCode,
        headers: response.headers,
        data: response.data  // 完整保存原始数据结构
      },
      timestamp: new Date().toISOString()
    };
    
    saveToJsonFile(fullResponse, 'ai-compare-v4-multi-result.json');
    
    if (response.statusCode === 200 && response.data.success) {
      console.log('🎉 多产品AI对比V4测试成功!');
    } else {
      console.log('⚠️  多产品AI对比V4请求返回非成功状态');
    }
    
  } catch (error) {
    console.error('❌ 多产品对比测试失败:', error);
    
    // 保存错误信息
    const errorResponse = {
      request: {
        url: apiUrl,
        method: 'POST',
        body: requestData
      },
      error: {
        message: error.message,
        stack: error.stack
      },
      timestamp: new Date().toISOString()
    };
    
    saveToJsonFile(errorResponse, 'ai-compare-v4-multi-error.json');
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🚀 开始运行AI产品对比V4测试套件...');
  
  // 测试1: 基础双产品对比
  await testAICompareV4();
  
  console.log('\n' + '='.repeat(50) + '\n');
  
  // 测试2: 多产品对比
  await testMultiProductCompare();
  
  console.log('\n🎯 所有测试完成!');
}

/**
 * 运行测试
 */
function runTest() {
  runAllTests();
}

// 如果直接运行此脚本文件，自动执行测试
if (require.main === module) {
  console.log('🚀 启动AI产品对比V4测试...');
  runTest();
}

// 导出函数
module.exports = {
  testAICompareV4,
  testMultiProductCompare,
  runAllTests,
  runTest
};
