/**
 * 用户对AI产品对比结果评分服务测试
 * 测试评分功能的基本操作
 */

const mongoose = require('mongoose');
const { comparisonRating } = require('../src/services/user');
const ComparisonRating = require('../src/models/ComparisonRating');
const ProductComparisonV4Cache = require('../src/models/ProductComparisonV4Cache');
const User = require('../src/models/User');

// 测试数据
const testData = {
  userId: null,
  comparisonCacheId: null,
  testRating: 4.5
};

/**
 * 运行评分服务测试
 */
async function runComparisonRatingTest() {
  try {
    console.log('🧪 开始用户评分服务测试...');
    
    // 连接数据库
    if (mongoose.connection.readyState !== 1) {
      await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/xuanxuan');
      console.log('✅ 数据库连接成功');
    }
    
    // 1. 查找测试用户
    console.log('\n1. 查找测试用户...');
    const testUser = await User.findOne({ isActive: true }).limit(1);
    if (!testUser) {
      console.log('❌ 未找到测试用户，请先创建用户');
      return;
    }
    testData.userId = testUser._id.toString();
    console.log(`✅ 找到测试用户: ${testUser.nickname} (${testData.userId})`);
    
    // 2. 查找测试对比缓存
    console.log('\n2. 查找测试对比缓存...');
    const testCache = await ProductComparisonV4Cache.findOne().limit(1);
    if (!testCache) {
      console.log('❌ 未找到测试对比缓存，请先生成产品对比结果');
      return;
    }
    testData.comparisonCacheId = testCache._id.toString();
    console.log(`✅ 找到测试对比缓存: ${testCache.productNames.join(' vs ')} (${testData.comparisonCacheId})`);
    
    // 3. 测试用户评分
    console.log('\n3. 测试用户评分...');
    const ratingResult = await comparisonRating.rateComparison(
      testData.userId,
      testData.comparisonCacheId,
      testData.testRating
    );
    
    if (ratingResult.success) {
      console.log('✅ 用户评分成功');
      console.log(`   - 用户评分: ${ratingResult.data.userRating}星`);
      console.log(`   - 平均评分: ${ratingResult.data.averageRating}星`);
      console.log(`   - 总评分数: ${ratingResult.data.totalRatings}个`);
      console.log(`   - 是否更新: ${ratingResult.data.isUpdate ? '是' : '否'}`);
    } else {
      console.log(`❌ 用户评分失败: ${ratingResult.error}`);
      return;
    }
    
    // 4. 测试获取评分统计
    console.log('\n4. 测试获取评分统计...');
    const statsResult = await comparisonRating.getComparisonRatingStats(
      testData.comparisonCacheId,
      testData.userId
    );
    
    if (statsResult.success) {
      console.log('✅ 获取评分统计成功');
      console.log(`   - 产品对比: ${statsResult.data.productNames.join(' vs ')}`);
      console.log(`   - 产品类别: ${statsResult.data.productCategory}`);
      console.log(`   - 平均评分: ${statsResult.data.averageRating}星`);
      console.log(`   - 总评分数: ${statsResult.data.totalRatings}个`);
      console.log(`   - 用户评分: ${statsResult.data.userRating ? statsResult.data.userRating.rating + '星' : '未评分'}`);
    } else {
      console.log(`❌ 获取评分统计失败: ${statsResult.error}`);
    }
    
    // 5. 测试更新评分
    console.log('\n5. 测试更新评分...');
    const newRating = 3.0;
    const updateResult = await comparisonRating.rateComparison(
      testData.userId,
      testData.comparisonCacheId,
      newRating
    );
    
    if (updateResult.success) {
      console.log('✅ 更新评分成功');
      console.log(`   - 新评分: ${updateResult.data.userRating}星`);
      console.log(`   - 平均评分: ${updateResult.data.averageRating}星`);
      console.log(`   - 是否更新: ${updateResult.data.isUpdate ? '是' : '否'}`);
    } else {
      console.log(`❌ 更新评分失败: ${updateResult.error}`);
    }
    
    // 6. 测试获取用户评分历史
    console.log('\n6. 测试获取用户评分历史...');
    const historyResult = await comparisonRating.getUserRatingHistory(testData.userId, 1, 5);
    
    if (historyResult.success) {
      console.log('✅ 获取用户评分历史成功');
      console.log(`   - 总评分数: ${historyResult.data.pagination.total}个`);
      console.log('   - 评分历史:');
      historyResult.data.ratings.forEach((rating, index) => {
        console.log(`     ${index + 1}. ${rating.productNames.join(' vs ')} - ${rating.rating}星 (${rating.ratedAt.toLocaleDateString()})`);
      });
    } else {
      console.log(`❌ 获取用户评分历史失败: ${historyResult.error}`);
    }
    
    // 7. 测试删除评分
    console.log('\n7. 测试删除评分...');
    const deleteResult = await comparisonRating.deleteUserRating(
      testData.userId,
      testData.comparisonCacheId
    );
    
    if (deleteResult.success) {
      console.log('✅ 删除评分成功');
      console.log(`   - 删除的评分: ${deleteResult.data.deletedRating}星`);
      console.log(`   - 删除后平均评分: ${deleteResult.data.averageRating}星`);
      console.log(`   - 删除后总评分数: ${deleteResult.data.totalRatings}个`);
    } else {
      console.log(`❌ 删除评分失败: ${deleteResult.error}`);
    }
    
    console.log('\n🎉 用户评分服务测试完成!');
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  } finally {
    // 关闭数据库连接
    if (mongoose.connection.readyState === 1) {
      await mongoose.disconnect();
      console.log('\n📝 数据库连接已关闭');
    }
  }
}

// 运行测试
if (require.main === module) {
  runComparisonRatingTest();
}

module.exports = {
  runComparisonRatingTest
};