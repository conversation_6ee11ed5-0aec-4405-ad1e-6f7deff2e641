/**
 * 用户对AI对比缓存结果评分测试脚本
 * 测试用户ID: 6886172268e9124c4d24a7f4
 * 测试对比结果ID: 688615e268e9124c4d24a796
 * 使用方法：node user-rating-test.js
 */

const fs = require('fs');
const path = require('path');

// 配置后端服务器地址
const BASE_URL = 'http://localhost:5000/api/v1';

// 测试数据
const TEST_USER_ID = '6886172268e9124c4d24a7f4';
const TEST_COMPARISON_CACHE_ID = '688615e268e9124c4d24a796';

/**
 * 保存数据到JSON文件
 */
function saveToJsonFile(data, filename = 'user-rating-result.json') {
  const filePath = path.join(__dirname, filename);
  const jsonData = JSON.stringify(data, null, 2);

  try {
    fs.writeFileSync(filePath, jsonData, 'utf8');
    console.log(`✅ 数据已保存到: ${filePath}`);
  } catch (error) {
    console.error('❌ 保存文件失败:', error);
  }
}

/**
 * 发送HTTP请求 (Node.js环境)
 */
function sendRequest(url, data, method = 'POST', headers = {}) {
  return new Promise((resolve, reject) => {
    const https = require('https');
    const http = require('http');
    const urlObj = new URL(url);

    const postData = data ? JSON.stringify(data) : '';
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port,
      path: urlObj.pathname,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData),
        ...headers
      }
    };

    const protocol = urlObj.protocol === 'https:' ? https : http;

    const req = protocol.request(options, (res) => {
      let responseData = '';

      res.on('data', (chunk) => {
        responseData += chunk;
      });

      res.on('end', () => {
        try {
          const jsonData = JSON.parse(responseData);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: jsonData
          });
        } catch (error) {
          reject(new Error('响应数据解析失败: ' + error.message));
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (postData) {
      req.write(postData);
    }
    req.end();
  });
}

/**
 * 模拟用户登录获取token (这里需要根据实际情况调整)
 */
async function getUserToken() {
  // 注意：这里需要根据你的实际认证方式来获取token
  // 可能需要先调用登录接口或者使用测试token
  console.log('⚠️  注意：需要配置有效的用户认证token');
  
  // 返回测试token，实际使用时需要替换为真实的token获取逻辑
  return 'your-test-token-here';
}

/**
 * 执行用户评分测试
 */
async function testUserRating() {
  console.log('🧪 开始测试用户对AI对比缓存结果评分接口...');
  console.log(`👤 测试用户ID: ${TEST_USER_ID}`);
  console.log(`📊 测试对比结果ID: ${TEST_COMPARISON_CACHE_ID}`);

  try {
    // 获取用户认证token
    const token = await getUserToken();
    
    const requestData = {
      comparisonCacheId: TEST_COMPARISON_CACHE_ID,
      rating: 4.5  // 测试评分：4.5星
    };
    
    const apiUrl = `${BASE_URL}/users/comparison-ratings`;
    
    const headers = {
      'Authorization': `Bearer ${token}`  // 根据实际认证方式调整
    };

    console.log('📡 正在发送评分请求...');
    console.log('请求数据:', requestData);
    
    const response = await sendRequest(apiUrl, requestData, 'POST', headers);
    
    console.log('✅ 请求完成!');
    console.log('📊 HTTP状态码:', response.statusCode);
    console.log('📊 响应数据:', response.data);
    
    // 保存完整响应数据
    const fullResponse = {
      request: {
        url: apiUrl,
        method: 'POST',
        headers: headers,
        body: requestData
      },
      response: {
        statusCode: response.statusCode,
        headers: response.headers,
        data: response.data
      },
      timestamp: new Date().toISOString()
    };
    
    saveToJsonFile(fullResponse);
    
    if (response.statusCode === 201) {
      console.log('🎉 评分成功!');
    } else {
      console.log('⚠️  评分请求返回非成功状态码');
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    
    // 保存错误信息
    const errorResponse = {
      error: {
        message: error.message,
        stack: error.stack
      },
      timestamp: new Date().toISOString()
    };
    
    saveToJsonFile(errorResponse, 'user-rating-error.json');
  }
}

/**
 * 运行测试
 */
function runTest() {
  testUserRating();
}

// 如果直接运行此脚本文件，自动执行测试
if (require.main === module) {
  console.log('🚀 启动用户评分测试...');
  runTest();
}

// 导出函数
module.exports = {
  testUserRating,
  runTest
};
