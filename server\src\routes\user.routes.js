const express = require('express');
const router = express.Router();
const { protect } = require('../middlewares/auth');
const userController = require('../controllers/userController');
const comparisonRatingController = require('../controllers/user/comparisonRatingController');

// 获取用户参与投票的问题列表
router.get('/me/voted-questions', protect, userController.getVotedQuestions);

// 获取用户发起的问题列表
router.get('/me/created-questions', protect, userController.getCreatedQuestions);

// 更新用户基本资料
router.put('/me/profile', protect, userController.updateProfile);

// 发送修改手机号验证码
router.post('/me/change-phone/send-code', protect, userController.sendChangePhoneCode);

// 修改手机号
router.put('/me/change-phone', protect, userController.changePhone);

// 用户对AI产品对比结果进行评分
router.post('/comparison-ratings', protect, comparisonRatingController.rateComparison);

module.exports = router;
